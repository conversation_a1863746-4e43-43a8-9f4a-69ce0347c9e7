<script lang="ts" setup>
import type { V1ManageUserLearningsLearningDetailIDGetResponseResult } from '@/api/api.model'
import { showToast } from 'vant'
import {
  V1CommonFileUploadDownloadFileIdPost,
  V1ManageUserLearningsLearningDetailId,
  V1ManageUserLearningsLearningDetailIdStartPut,
} from '@/api/api.req'
import { MediaViewer } from '@/components/MediaViewer'
import { LearningStatus, LearningStatusOptions } from '@/enums/learning'
import { useAsyncData } from '@/hooks/useAsyncData'
import { getOptionLabel, secondsToHMS } from '@/utils'

const route = useRoute()
const router = useRouter()

const id = route.query.id as string

// 获取学习详情数据
const learningDetailApi = useAsyncData(
  async () => {
    if (!id)
      return null
    const resp = await V1ManageUserLearningsLearningDetailId({
      learningDetailId: id,
    })
    return resp
  },
  null as V1ManageUserLearningsLearningDetailIDGetResponseResult | null,
)

// 计算属性
const learningDetail = computed(() => learningDetailApi.data.value)

const statusText = computed(() => {
  if (!learningDetail.value?.status)
    return ''
  return getOptionLabel(LearningStatusOptions, learningDetail.value.status) || ''
})

const durationText = computed(() => {
  const detail = learningDetail.value
  if (!detail)
    return { required: '', learned: '' }

  const requiredSeconds = detail.requireDuration ? Number.parseInt(detail.requireDuration) : 0
  const learnedSeconds = detail.duration ? Number.parseInt(detail.duration) : 0

  return {
    required: secondsToHMS(requiredSeconds),
    learned: secondsToHMS(learnedSeconds),
  }
})

const currentMaterialIndex = ref(0)

const currentMaterial = computed(() => {
  const materials = learningDetail.value?.materialList || []
  return materials[currentMaterialIndex.value]
})

// 当前材料的文件URL
const currentFileUrl = ref('')

// 转换当前材料数据格式以适配MediaViewer组件
const currentMediaItem = computed(() => {
  const material = currentMaterial.value
  const fileUrl = currentFileUrl.value
  console.log('currentMediaItem computed:')
  console.log('  - material:', material)
  console.log('  - fileUrl:', fileUrl)
  console.log('  - material.fileType:', material?.fileType)

  if (!material)
    return null

  const mediaItem = {
    id: material.fileId || material.id || '',
    name: material.filename || '学习材料',
    url: fileUrl,
    type: material.fileType,
    size: material.fileSize ? Number.parseInt(material.fileSize) : undefined,
    createTime: material.lastUpdateTime ? String(material.lastUpdateTime) : undefined,
  }

  console.log('Generated mediaItem:', mediaItem)
  return mediaItem
})

const materialCount = computed(() => {
  return learningDetail.value?.materialList?.length || 0
})

const canGoPrev = computed(() => currentMaterialIndex.value > 0)
const canGoNext = computed(() => currentMaterialIndex.value < materialCount.value - 1)

// 初始化数据
learningDetailApi.load()

// 方法
function goPrev() {
  console.log('goPrev clicked, canGoPrev:', canGoPrev.value, 'currentIndex:', currentMaterialIndex.value)
  if (canGoPrev.value) {
    currentMaterialIndex.value--
    console.log('Index changed to:', currentMaterialIndex.value)
  }
}

function goNext() {
  console.log('goNext clicked, canGoNext:', canGoNext.value, 'currentIndex:', currentMaterialIndex.value)
  if (canGoNext.value) {
    currentMaterialIndex.value++
    console.log('Index changed to:', currentMaterialIndex.value)
  }
}

function goToExam() {
  if (!learningDetail.value?.examRecordId)
    return

  router.push({
    path: '/exam/detail',
    query: {
      id: learningDetail.value.paperId,
    },
  })
}

async function startLearning() {
  // 在学习详情页面直接开始学习，无需跳转到其他页面
  try {
    // 调用开始学习的API
    await V1ManageUserLearningsLearningDetailIdStartPut({ learningDetailId: id })

    // 重新加载学习详情数据以获取最新状态
    await learningDetailApi.load()

    // 显示成功提示
    showToast('开始学习成功')
  }
  catch (error) {
    console.error('开始学习失败:', error)
    showToast('开始学习失败，请重试')
  }
}

// 获取文件下载URL
async function getFileDownloadUrl(fileId?: string): Promise<string> {
  if (!fileId)
    return ''

  try {
    const response = await V1CommonFileUploadDownloadFileIdPost({
      fileId,
    })

    // axios拦截器会自动返回result字段，所以response就是文件下载URL
    return response || ''
  }
  catch (error) {
    console.error('获取文件下载地址失败:', error)
    return ''
  }
}

// 重试加载材料
function retryLoadMaterial() {
  showToast('正在重新加载...')
  // 重新获取当前材料的文件URL
  loadCurrentFileUrl()
}

// 加载当前材料的文件URL
async function loadCurrentFileUrl() {
  const material = currentMaterial.value
  console.log('loadCurrentFileUrl called, material:', material)

  if (!material?.fileId) {
    console.log('No material or fileId, clearing URL')
    currentFileUrl.value = ''
    return
  }

  try {
    console.log('Getting download URL for fileId:', material.fileId)
    const url = await getFileDownloadUrl(material.fileId)
    console.log('Got download URL:', url)
    currentFileUrl.value = url
  }
  catch (error) {
    console.error('加载文件URL失败:', error)
    currentFileUrl.value = ''
  }
}

// 监听当前材料变化，自动加载文件URL
watch(currentMaterial, (newMaterial) => {
  console.log('currentMaterial changed:', newMaterial)
  if (newMaterial?.fileId) {
    console.log('Loading file URL for fileId:', newMaterial.fileId)
    loadCurrentFileUrl()
  }
  else {
    console.log('No fileId, clearing URL')
    currentFileUrl.value = ''
  }
}, { immediate: true })

// 监听材料索引变化
watch(currentMaterialIndex, (newIndex) => {
  console.log('currentMaterialIndex changed to:', newIndex)
}, { immediate: true })
</script>

<template>
  <div class="learning-detail-page">
    <VanNavBar title="学习详情" left-text="返回" left-arrow @click-left="router.back()" />

    <div v-if="learningDetailApi.loading.value" class="loading-container">
      <VanLoading type="spinner" />
    </div>

    <div v-else-if="learningDetail" class="page-content">
      <!-- 学习标题区域 -->
      <CardBox class="mb-4">
        <div class="learning-header">
          <h2 class="learning-title">
            {{ learningDetail.name }}
          </h2>
          <div class="learning-status">
            <ColorTag>{{ statusText }}</ColorTag>
          </div>
        </div>

        <!-- 学习时长信息 -->
        <div class="duration-info">
          <div class="duration-item">
            <span class="label">要求时长：</span>
            <span class="value">{{ durationText.required }}</span>
          </div>
          <div class="duration-item">
            <span class="label">已学时长：</span>
            <span class="value">{{ durationText.learned }}</span>
          </div>
        </div>

        <!-- 考试信息 -->
        <div v-if="learningDetail.paperName" class="exam-info">
          <div class="exam-item">
            <span class="exam-label">考试试卷名称</span>
            <div class="exam-content">
              <span class="exam-name">{{ learningDetail.paperName }}</span>
              <VanIcon
                v-if="learningDetail.examRecordId"
                name="arrow"
                class="exam-arrow"
                @click="goToExam"
              />
            </div>
          </div>
        </div>
      </CardBox>

      <!-- 材料导航区域 -->
      <CardBox v-if="materialCount > 0" class="mb-4">
        <div class="material-navigation">
          <VanButton
            :disabled="!canGoPrev"
            size="small"
            @click="goPrev"
          >
            上一题
          </VanButton>

          <div class="material-counter">
            {{ currentMaterialIndex + 1 }}/{{ materialCount }}
          </div>

          <VanButton
            :disabled="!canGoNext"
            size="small"
            @click="goNext"
          >
            下一题
          </VanButton>
        </div>
      </CardBox>

      <!-- 学习内容区域 -->
      <div v-if="currentMediaItem" class="mb-4">
        <MediaViewer
          :key="`${currentMaterialIndex}-${currentMediaItem.id}-${currentMediaItem.url}`"
          :media-item="currentMediaItem"
          :show-title="true"
          :autoplay="true"
          :allow-download="false"
          :water-mark="learningDetail?.name || '学习系统'"
          @retry="retryLoadMaterial"
        />
      </div>

      <!-- 无学习材料时的提示 -->
      <CardBox v-else-if="learningDetail && materialCount === 0" class="mb-4">
        <div class="empty-materials">
          <VanEmpty description="暂无学习材料" />
        </div>
      </CardBox>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <VanButton
          v-if="learningDetail.status !== LearningStatus.FINISH"
          type="primary"
          block
          @click="startLearning"
        >
          开始学习
        </VanButton>

        <VanButton
          v-if="learningDetail.examRecordId && learningDetail.status === LearningStatus.FINISH"
          type="primary"
          block
          @click="goToExam"
        >
          去考试
        </VanButton>
      </div>
    </div>

    <div v-else class="error-container">
      <EmptyData />
    </div>
  </div>
</template>

<style lang="less" scoped>
.learning-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.page-content {
  padding: 16px;
}

.learning-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .learning-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
    flex: 1;
    margin-right: 12px;
  }

  .learning-status {
    flex-shrink: 0;
  }
}

.duration-info {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;

  .duration-item {
    .label {
      color: #666;
      font-size: 14px;
    }

    .value {
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.exam-info {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;

  .exam-item {
    .exam-label {
      display: block;
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .exam-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .exam-name {
        color: #333;
        font-size: 14px;
      }

      .exam-arrow {
        color: #1989fa;
        cursor: pointer;
      }
    }
  }
}

.material-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .material-counter {
    font-size: 14px;
    color: #666;
  }
}

.empty-materials {
  padding: 20px 0;
  text-align: center;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: white;
  border-top: 1px solid #f0f0f0;
  z-index: 10;
}
</style>
