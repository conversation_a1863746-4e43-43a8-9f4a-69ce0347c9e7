<script lang="ts" setup>
import FilePreview from './FilePreview.vue'
import VideoPlayer from './VideoPlayer.vue'

interface MediaItem {
  id: string | number
  name: string
  url: string
  type?: string
  size?: number
  createTime?: string
}

interface Props {
  mediaItem: MediaItem
  showTitle?: boolean
  autoplay?: boolean
  allowDownload?: boolean
  waterMark?: string
}

const props = withDefaults(defineProps<Props>(), {
  showTitle: true,
  autoplay: false,
  allowDownload: false,
  waterMark: '智工育匠',
})

const emit = defineEmits<{
  retry: []
}>()

// 获取文件扩展名（用于FilePreview组件）
const fileExtension = computed(() => {
  const mediaItem = props.mediaItem
  console.log('fileExtension computed - mediaItem:', mediaItem)

  // 如果后端返回的type不是标准的媒体类型，则可能是扩展名
  if (mediaItem?.type && !['video', 'audio', 'image', 'file'].includes(mediaItem.type.toLowerCase())) {
    console.log('Using mediaItem.type as extension:', mediaItem.type)
    return mediaItem.type.toLowerCase()
  }

  const fileName = mediaItem?.name || mediaItem?.url || ''
  console.log('Using fileName:', fileName)
  const lastDot = fileName.lastIndexOf('.')
  const extension = lastDot > -1 ? fileName.substring(lastDot + 1).toLowerCase() : ''
  console.log('Extracted extension:', extension)
  return extension
})

// 判断媒体类型
const mediaType = computed(() => {
  const mediaItem = props.mediaItem
  console.log('MediaViewer - mediaType computed, mediaItem:', mediaItem)

  // 如果后端直接返回了媒体类型，直接使用
  if (mediaItem?.type) {
    const type = mediaItem.type.toLowerCase()
    console.log('Backend provided type:', type)

    // 只支持视频类型
    if (type === 'video') {
      console.log('Using backend type directly: video')
      return 'video'
    }
  }

  // 否则根据文件扩展名判断
  const ext = fileExtension.value
  console.log('Fallback to extension-based detection, ext:', ext)

  // 视频类型
  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v']
  if (videoTypes.includes(ext)) {
    console.log('Detected as video type by extension')
    return 'video'
  }

  // 其他文件类型都使用文件预览
  console.log('Detected as file type')
  return 'file'
})

function handleRetry() {
  emit('retry')
}

// 监听mediaItem变化，确保组件正确更新
watch(() => props.mediaItem, (newItem) => {
  console.log('MediaViewer - mediaItem changed:', newItem)
}, { deep: true, immediate: true })
</script>

<template>
  <div class="media-viewer">
    <!-- 视频播放器 -->
    <VideoPlayer
      v-if="mediaType === 'video'"
      :src="mediaItem.url"
      :title="showTitle ? mediaItem.name : ''"
      :create-time="mediaItem.createTime"
      :autoplay="autoplay"
      @retry="handleRetry"
    />

    <!-- 文件预览器 -->
    <FilePreview
      v-else
      :file-url="mediaItem.url"
      :file-name="mediaItem.name"
      :file-type="fileExtension"
      :file-size="mediaItem.size"
      :allow-download="allowDownload"
      :water-mark="waterMark"
      @retry="handleRetry"
    />
  </div>
</template>

<style lang="less" scoped>
.media-viewer {
  width: 100%;
}

.media-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;

  .media-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    word-break: break-all;
  }

  .media-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 12px;
    color: #666;

    .file-type {
      background: #e8f4ff;
      color: #1989fa;
      padding: 2px 6px;
      border-radius: 4px;
    }
  }
}
</style>
